# Camera Settings
camera_resolution: "1280x720"
camera_fps: 30
exposure:
  d435i: 150
  zed2i: 100

# ZED Camera Settings
zed:
  enable_self_calibration: false  # Set to true to enable self-calibration on startup
  depth_mode: "PERFORMANCE"       # Options: PERFORMANC<PERSON>, QUALITY, ULTRA, NEURAL
  depth_stabilization: true
  depth_minimum_distance: 200     # Minimum depth distance in mm

# Storage Settings
dataset_root_dir: "../the-dataset"
directory_format: "{date}/{lighting}/{background_id}"

# Logging
log_level: "INFO"
log_file: "multicam_collector.log"
