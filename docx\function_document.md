# 数据集采集GUI程序功能方案

一个好的采集软件能极大提升您在“受控环境”下进行数据采集的效率和规范性。以下是针对您“从五台RGB-D相机中保存图片”需求的GUI功能梳理。

#### 一、 核心功能 (必备)

这些是程序必须具备的基础功能，确保数据可以被正确、完整地采集。

1. **相机管理与连接**
    - **自动发现与连接**：程序启动时，应能自动检测并尝试连接所有（5台）可用的RGB-D相机(有4台intel D435i的real sense和一台zed 2i的real sense)。
    - **状态显示**：在界面上清晰地显示每台相机的连接状态（如：在线/绿色、离线/红色）。
    - **独立控制**：可以单独重新连接或刷新某一台掉线的相机，而不需要重启整个程序。
2. **实时图像预览**
    - **多视图同步显示**：界面上需要有一个区域，能同时显示所有5台相机的实时画面。
    - **双通道预览**：对于每一台相机，都应该能同时预览 **RGB彩色图像** 和 **深度图像 (Depth Map)**。深度图最好能以伪彩色的方式（或灰度图）进行可视化，方便人眼观察。
    - **布局**：一个 2x5 或 3x4 的网格布局会很直观，每台相机占据一个格子，格子里并列显示RGB和Depth画面。
3. **数据采集与保存**
    - **一键触发**：一个醒目的“**采集 (Capture)**”按钮，点击后能同步触发所有5台相机进行拍照。
    - **元数据 (Metadata) 输入**：在采集前，必须能方便地输入这批数据的关键标签信息。
        - **光照等级 (Lighting Level)**：使用下拉菜单，选项为“极暗”, “暗光”, “正常光”。
        - **背景标识 (Background ID)**：例如 blue_cloth, wood_desk, default_bg。
        - **序列号 (Sequence/Shot Number)**：程序应能自动为每次采集递增编号 (如 001, 002, ...)，也允许手动修改。
    - **自动化目录与文件管理**：这是整个程序最核心的自动化功能。点击“采集”后，程序应自动完成：
        1. **创建目录**：根据元数据自动创建层级目录，自动按“日期/光照/场景/”建目录，例如： D:/Dataset/日期/正常光/背景1/。
        2. **文件命名**：在该目录下，保存来自5台相机的数据，并用规范的名称区分，文件命名：20250630T101523_RGB.png / \_Depth.tiff
            - metadata.json (一个包含当前所有元数据信息的文件，非常重要！)
4. **基本设置**
    - **根目录选择**：允许用户通过浏览按钮选择一个用于保存所有数据集的根目录。
    - **相机参数**：提供基础的相机参数设置，如分辨率、曝光（如果API支持）。

#### 二、 建议功能 (提升效率和数据质量)

1. **工作流优化**
    - **元数据锁定**：在连续拍摄同一物体、同一场景时，可以“锁定”元数据，操作员只需不断点击“采集”即可。
    - **快捷键**：为“采集”按钮设置一个快捷键（如空格键）。
    - **日志窗口**：显示程序运行信息和报错，方便排查问题。

#### 三、 理想工作流程

1. **准备阶段**：启动程序，连接相机，选择保存目录。
2. **采集阶段**：
    1. 摆放物体。
    2. 在GUI中，输入/选择、光照等级、背景标识。
    3. 检查所有预览窗口。
    4. 点击“**采集**”或按快捷键。
    5. 程序自动创建目录并保存所有文件和元数据。序列号自动递增。
    6. 轻微移动物体，重复采集。
    7. 更换物体或场景、修改亮度后，修改对应的元数据，继续采集。