<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RGB-D数据集采集系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow: hidden;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
            height: calc(100vh - 40px);
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 600;
        }

        .system-stats {
            display: flex;
            align-items: center;
            gap: 25px;
            font-size: 14px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .cameras-online {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .main-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        .camera-section {
            flex: 2.5;
            padding: 25px;
            background: #f8f9fa;
            border-right: 2px solid #e9ecef;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .camera-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            flex: 1;
            align-content: start;
        }

        .camera-item {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 20px;
            display: flex;
            flex-direction: column;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            min-height: 250px;
        }

        .camera-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .camera-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .camera-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
        }

        .camera-status {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            padding: 4px 10px;
            border-radius: 15px;
            font-weight: 500;
        }

        .status-online {
            color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .status-offline {
            color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }

        .camera-preview {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .preview-panel {
            flex: 1;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            position: relative;
            overflow: hidden;
            min-height: 140px;
        }

        .preview-panel.offline {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
        }

        .preview-label {
            position: absolute;
            top: 10px;
            left: 12px;
            font-size: 11px;
            background: rgba(0, 0, 0, 0.6);
            padding: 4px 8px;
            border-radius: 6px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .resolution-info {
            position: absolute;
            bottom: 10px;
            right: 12px;
            font-size: 10px;
            background: rgba(0, 0, 0, 0.6);
            padding: 4px 8px;
            border-radius: 6px;
        }

        .camera-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #ecf0f1;
            font-size: 12px;
            color: #7f8c8d;
        }

        .depth-indicator {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #9b59b6;
            font-weight: 500;
        }

        .depth-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #9b59b6;
        }

        .control-section {
            flex: 1;
            background: white;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .control-content {
            flex: 1;
            padding: 25px;
            overflow-y: auto;
            padding-bottom: 120px;
        }

        .control-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #3498db;
            margin-bottom: 20px;
        }

        .control-group h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #34495e;
            font-size: 13px;
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .directory-selector {
            display: flex;
            gap: 10px;
        }

        .directory-input {
            flex: 1;
        }

        .browse-btn {
            padding: 10px 20px;
            background: #95a5a6;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .browse-btn:hover {
            background: #7f8c8d;
        }

        .metadata-lock {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
        }

        .metadata-lock input[type="checkbox"] {
            width: auto;
        }

        .sequence-counter {
            background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 15px;
        }

        .sequence-number {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-top: 5px;
        }

        .sequence-label {
            font-size: 14px;
            color: #7f8c8d;
            font-weight: 500;
        }

        .log-section {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            height: 120px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
        }

        .log-info {
            color: #3498db;
        }

        .log-success {
            color: #27ae60;
        }

        .log-error {
            color: #e74c3c;
        }

        .settings-panel {
            background: #f1f2f6;
            padding: 15px;
            border-radius: 8px;
        }

        .settings-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .settings-row:last-child {
            margin-bottom: 0;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #bdc3c7;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #27ae60;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active::before {
            transform: translateX(26px);
        }

        .floating-capture-section {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            padding: 20px 25px;
            box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .capture-btn {
            width: 100%;
            padding: 16px;
            background: white;
            color: #27ae60;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .capture-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .capture-btn:active {
            transform: translateY(0);
        }

        .shortcut-badge {
            background: #27ae60;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .capture-status {
            text-align: center;
            margin-top: 8px;
            color: white;
            font-size: 12px;
            opacity: 0.9;
        }

        .control-content::-webkit-scrollbar {
            width: 6px;
        }

        .control-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .control-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .control-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        @media (max-width: 1400px) {
            .camera-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 1000px) {
            .main-content {
                flex-direction: column;
            }
            
            .camera-section {
                flex: none;
                height: 50vh;
            }
            
            .camera-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>RGB-D数据集采集系统</h1>
            <div class="system-stats">
                <div class="cameras-online">
                    <span>📹</span>
                    <span id="cameraCount">4/5 相机在线</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span>系统运行中</span>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="camera-section">
                <div class="camera-grid">
                    <div class="camera-item">
                        <div class="camera-header">
                            <div class="camera-title">相机 #1</div>
                            <div class="camera-status status-online">
                                <div class="status-dot" style="animation: none; background: #27ae60; width: 6px; height: 6px;"></div>
                                在线
                            </div>
                        </div>
                        <div class="camera-preview">
                            <div class="preview-panel">
                                <div class="preview-label">RGB LIVE</div>
                                <div class="resolution-info">1024×1024</div>
                                实时预览
                            </div>
                        </div>
                        <div class="camera-info">
                            <span>FPS: 30</span>
                            <div class="depth-indicator">
                                <div class="depth-dot"></div>
                                <span>深度数据</span>
                            </div>
                        </div>
                    </div>

                    <div class="camera-item">
                        <div class="camera-header">
                            <div class="camera-title">相机 #2</div>
                            <div class="camera-status status-online">
                                <div class="status-dot" style="animation: none; background: #27ae60; width: 6px; height: 6px;"></div>
                                在线
                            </div>
                        </div>
                        <div class="camera-preview">
                            <div class="preview-panel">
                                <div class="preview-label">RGB LIVE</div>
                                <div class="resolution-info">1024×1024</div>
                                实时预览
                            </div>
                        </div>
                        <div class="camera-info">
                            <span>FPS: 30</span>
                            <div class="depth-indicator">
                                <div class="depth-dot"></div>
                                <span>深度数据</span>
                            </div>
                        </div>
                    </div>

                    <div class="camera-item">
                        <div class="camera-header">
                            <div class="camera-title">相机 #3</div>
                            <div class="camera-status status-offline">
                                <div class="status-dot" style="animation: none; background: #e74c3c; width: 6px; height: 6px;"></div>
                                离线
                            </div>
                        </div>
                        <div class="camera-preview">
                            <div class="preview-panel offline">
                                <div class="preview-label">OFFLINE</div>
                                <div class="resolution-info">---</div>
                                连接中...
                            </div>
                        </div>
                        <div class="camera-info">
                            <span>FPS: --</span>
                            <div class="depth-indicator" style="color: #95a5a6;">
                                <div class="depth-dot" style="background: #95a5a6;"></div>
                                <span>无深度数据</span>
                            </div>
                        </div>
                    </div>

                    <div class="camera-item">
                        <div class="camera-header">
                            <div class="camera-title">相机 #4</div>
                            <div class="camera-status status-online">
                                <div class="status-dot" style="animation: none; background: #27ae60; width: 6px; height: 6px;"></div>
                                在线
                            </div>
                        </div>
                        <div class="camera-preview">
                            <div class="preview-panel">
                                <div class="preview-label">RGB LIVE</div>
                                <div class="resolution-info">1024×1024</div>
                                实时预览
                            </div>
                        </div>
                        <div class="camera-info">
                            <span>FPS: 30</span>
                            <div class="depth-indicator">
                                <div class="depth-dot"></div>
                                <span>深度数据</span>
                            </div>
                        </div>
                    </div>

                    <div class="camera-item">
                        <div class="camera-header">
                            <div class="camera-title">相机 #5</div>
                            <div class="camera-status status-online">
                                <div class="status-dot" style="animation: none; background: #27ae60; width: 6px; height: 6px;"></div>
                                在线
                            </div>
                        </div>
                        <div class="camera-preview">
                            <div class="preview-panel">
                                <div class="preview-label">RGB LIVE</div>
                                <div class="resolution-info">1024×1024</div>
                                实时预览
                            </div>
                        </div>
                        <div class="camera-info">
                            <span>FPS: 30</span>
                            <div class="depth-indicator">
                                <div class="depth-dot"></div>
                                <span>深度数据</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="control-section">
                <div class="control-content">
                    <div class="control-group">
                        <h3>📁 数据保存设置</h3>
                        <div class="form-group">
                            <label>根目录路径</label>
                            <div class="directory-selector">
                                <input type="text" class="directory-input" value="D:\Dataset" readonly>
                                <button class="browse-btn">浏览</button>
                            </div>
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>🏷️ 元数据配置</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label>光照等级</label>
                                <select>
                                    <option value="normal">正常光</option>
                                    <option value="low">暗光</option>
                                    <option value="extreme_low">极暗</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>背景标识</label>
                                <select>
                                    <option value="blue_cloth">蓝色布料</option>
                                    <option value="wood_desk">木质桌面</option>
                                    <option value="default_bg">默认背景</option>
                                    <option value="white_wall">白色墙面</option>
                                </select>
                            </div>
                        </div>
                        <div class="metadata-lock">
                            <input type="checkbox" id="lockMetadata">
                            <label for="lockMetadata">锁定元数据 (连续拍摄)</label>
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>📊 采集状态</h3>
                        <div class="sequence-counter">
                            <div class="sequence-label">当前序列号</div>
                            <div class="sequence-number">001</div>
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>⚙️ 相机设置</h3>
                        <div class="settings-panel">
                            <div class="settings-row">
                                <span>自动曝光</span>
                                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                            </div>
                            <div class="settings-row">
                                <span>深度滤波</span>
                                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                            </div>
                            <div class="settings-row">
                                <span>同步触发</span>
                                <div class="toggle-switch active" onclick="toggleSwitch(this)"></div>
                            </div>
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>📝 系统日志</h3>
                        <div class="log-section" id="logSection">
                            <div class="log-entry log-info">[14:23:15] 系统启动完成</div>
                            <div class="log-entry log-success">[14:23:16] 相机 #1,2,4,5 连接成功</div>
                            <div class="log-entry log-error">[14:23:17] 相机 #3 连接失败，正在重试...</div>
                            <div class="log-entry log-info">[14:23:20] 目录创建: D:\Dataset\20250630</div>
                            <div class="log-entry log-success">[14:23:25] 元数据配置已锁定</div>
                        </div>
                    </div>
                </div>

                <div class="floating-capture-section">
                    <button class="capture-btn" onclick="captureData()">
                        🎯 开始采集数据
                        <span class="shortcut-badge">空格</span>
                    </button>
                    <div class="capture-status">准备就绪 • 按空格键或点击按钮开始采集</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let sequenceNumber = 1;
        let isCapturing = false;
        let onlineCameras = 4;

        function toggleSwitch(element) {
            element.classList.toggle('active');
        }

        function updateCameraCount() {
            document.getElementById('cameraCount').textContent = `${onlineCameras}/5 相机在线`;
        }

        function captureData() {
            if (isCapturing) return;
            
            isCapturing = true;
            const btn = document.querySelector('.capture-btn');
            const status = document.querySelector('.capture-status');
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '📸 采集中... <span class="shortcut-badge">等待</span>';
            btn.style.background = '#f39c12';
            status.textContent = '正在采集RGB和深度数据，请稍候...';
            
            addLogEntry('开始采集数据...', 'info');
            addLogEntry(`同步触发${onlineCameras}台相机...`, 'info');
            
            setTimeout(() => {
                addLogEntry(`采集完成 - 序列号: ${String(sequenceNumber).padStart(3, '0')}`, 'success');
                addLogEntry(`已保存 ${onlineCameras} 组RGB图像和深度数据`, 'success');
                addLogEntry('元数据文件已生成', 'success');
                
                sequenceNumber++;
                document.querySelector('.sequence-number').textContent = String(sequenceNumber).padStart(3, '0');
                
                btn.innerHTML = originalText;
                btn.style.background = 'white';
                status.textContent = '采集完成 • 按空格键或点击按钮开始下一次采集';
                isCapturing = false;
            }, 2500);
        }

        function addLogEntry(message, type) {
            const logSection = document.getElementById('logSection');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logSection.appendChild(logEntry);
            logSection.scrollTop = logSection.scrollHeight;
            
            if (logSection.children.length > 20) {
                logSection.removeChild(logSection.firstChild);
            }
        }

        document.addEventListener('keydown', function(event) {
            if (event.code === 'Space' && !event.target.matches('input, select, textarea')) {
                event.preventDefault();
                captureData();
            }
        });

        // 模拟相机状态更新
        setInterval(() => {
            const offlineCamera = document.querySelector('.status-offline');
            if (offlineCamera && Math.random() > 0.7) {
                offlineCamera.className = 'camera-status status-online';
                offlineCamera.innerHTML = '<div class="status-dot" style="animation: none; background: #27ae60; width: 6px; height: 6px;"></div>在线';
                
                const cameraItem = offlineCamera.closest('.camera-item');
                const previewPanel = cameraItem.querySelector('.preview-panel');
                previewPanel.className = 'preview-panel';
                previewPanel.innerHTML = '<div class="preview-label">RGB LIVE</div><div class="resolution-info">1024×1024</div>实时预览';
                
                const cameraInfo = cameraItem.querySelector('.camera-info');
                cameraInfo.innerHTML = '<span>FPS: 30</span><div class="depth-indicator"><div class="depth-dot"></div><span>深度数据</span></div>';
                
                onlineCameras = 5;
                updateCameraCount();
                addLogEntry('相机 #3 重新连接成功', 'success');
            }
        }, 8000);

        // 初始化
        updateCameraCount();
    </script>
</body>
</html>